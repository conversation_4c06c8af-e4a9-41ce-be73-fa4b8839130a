from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal

from .models import (
    RevenueStream, Expense, ExpenseCategory, CashFlowProjection,
    Budget, BudgetAllocation, FinancialKPI, FinancialReport
)
from .serializers import (
    RevenueStreamSerializer, RevenueStreamListSerializer,
    ExpenseSerializer, ExpenseListSerializer, ExpenseCategorySerializer,
    CashFlowProjectionSerializer, BudgetSerializer, BudgetListSerializer,
    BudgetAllocationSerializer, FinancialKPISerializer,
    FinancialSummarySerializer, DepartmentFinancialSerializer,
    FinancialReportSerializer, FinancialReportListSerializer
)


class RevenueStreamViewSet(viewsets.ModelViewSet):
    """Revenue stream management viewset"""
    queryset = RevenueStream.objects.all().select_related('client', 'project', 'sales_rep').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status', 'client', 'project', 'sales_rep']
    search_fields = ['title', 'description', 'client__name', 'project__name']
    ordering_fields = ['amount', 'invoice_date', 'due_date', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return RevenueStreamListSerializer
        return RevenueStreamSerializer

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get revenue summary statistics"""
        queryset = self.get_queryset()
        
        # Basic statistics
        total_revenue = queryset.filter(status='received').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        pending_revenue = queryset.filter(status='pending').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        overdue_count = queryset.filter(
            status='pending',
            due_date__lt=timezone.now().date()
        ).count()
        
        # Monthly revenue trend (last 6 months)
        six_months_ago = timezone.now().date() - timedelta(days=180)
        monthly_revenue = []
        
        for i in range(6):
            month_start = six_months_ago + timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            
            month_total = queryset.filter(
                status='received',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0
            
            monthly_revenue.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': float(month_total)
            })
        
        # Revenue by type
        revenue_by_type = queryset.filter(status='received').values('type').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')
        
        summary = {
            'total_revenue': float(total_revenue),
            'pending_revenue': float(pending_revenue),
            'overdue_count': overdue_count,
            'monthly_trend': monthly_revenue,
            'revenue_by_type': list(revenue_by_type)
        }
        
        return Response(summary)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue revenue streams"""
        overdue_revenues = self.get_queryset().filter(
            status='pending',
            due_date__lt=timezone.now().date()
        )
        
        serializer = self.get_serializer(overdue_revenues, many=True)
        return Response(serializer.data)


class ExpenseViewSet(viewsets.ModelViewSet):
    """Expense management viewset"""
    queryset = Expense.objects.all().select_related('category', 'team_member', 'project', 'approved_by').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status', 'category', 'team_member', 'project']
    search_fields = ['title', 'description', 'team_member__user__first_name', 'team_member__user__last_name']
    ordering_fields = ['amount', 'expense_date', 'due_date', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ExpenseListSerializer
        return ExpenseSerializer

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get expense summary statistics"""
        queryset = self.get_queryset()
        
        # Basic statistics
        total_expenses = queryset.filter(status='paid').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        pending_expenses = queryset.filter(status='pending').aggregate(
            total=Sum('net_amount')
        )['total'] or 0
        
        pending_approval = queryset.filter(status='pending').count()
        
        # Monthly expense trend (last 6 months)
        six_months_ago = timezone.now().date() - timedelta(days=180)
        monthly_expenses = []
        
        for i in range(6):
            month_start = six_months_ago + timedelta(days=30*i)
            month_end = month_start + timedelta(days=30)
            
            month_total = queryset.filter(
                status='paid',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0
            
            monthly_expenses.append({
                'month': month_start.strftime('%Y-%m'),
                'expenses': float(month_total)
            })
        
        # Expenses by type
        expenses_by_type = queryset.filter(status='paid').values('type').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')
        
        # Expenses by department
        expenses_by_dept = queryset.filter(
            status='paid',
            team_member__isnull=False
        ).values('team_member__department').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')
        
        summary = {
            'total_expenses': float(total_expenses),
            'pending_expenses': float(pending_expenses),
            'pending_approval': pending_approval,
            'monthly_trend': monthly_expenses,
            'expenses_by_type': list(expenses_by_type),
            'expenses_by_department': list(expenses_by_dept)
        }
        
        return Response(summary)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve an expense"""
        expense = self.get_object()
        
        if expense.status != 'pending':
            return Response(
                {'error': 'يمكن الموافقة على المصروفات المعلقة فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        expense.status = 'approved'
        expense.approved_by = request.user
        expense.save()
        
        serializer = self.get_serializer(expense)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject an expense"""
        expense = self.get_object()
        
        if expense.status != 'pending':
            return Response(
                {'error': 'يمكن رفض المصروفات المعلقة فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        expense.status = 'rejected'
        expense.approved_by = request.user
        expense.save()
        
        serializer = self.get_serializer(expense)
        return Response(serializer.data)


class ExpenseCategoryViewSet(viewsets.ModelViewSet):
    """Expense category management viewset"""
    queryset = ExpenseCategory.objects.all().order_by('name')
    serializer_class = ExpenseCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """Get expense categories in tree structure"""
        categories = self.get_queryset().filter(parent__isnull=True)
        
        def build_tree(category):
            data = ExpenseCategorySerializer(category).data
            children = ExpenseCategory.objects.filter(parent=category)
            if children.exists():
                data['children'] = [build_tree(child) for child in children]
            return data
        
        tree_data = [build_tree(cat) for cat in categories]
        return Response(tree_data)


class CashFlowProjectionViewSet(viewsets.ModelViewSet):
    """Cash flow projection management viewset"""
    queryset = CashFlowProjection.objects.all().order_by('-period_start')
    serializer_class = CashFlowProjectionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['period_type']
    ordering_fields = ['period_start', 'projected_revenue', 'actual_revenue']
    ordering = ['-period_start']

    @action(detail=False, methods=['get'])
    def current_year(self, request):
        """Get cash flow projections for current year"""
        current_year = timezone.now().year
        projections = self.get_queryset().filter(
            period_start__year=current_year
        )
        
        serializer = self.get_serializer(projections, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def auto_update(self, request):
        """Auto-update actual values from revenue and expense data"""
        updated_count = 0

        for projection in self.get_queryset():
            # Calculate actual revenue
            actual_revenue = RevenueStream.objects.filter(
                status='received',
                payment_date__gte=projection.period_start,
                payment_date__lte=projection.period_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Calculate actual expenses
            actual_expenses = Expense.objects.filter(
                status='paid',
                payment_date__gte=projection.period_start,
                payment_date__lte=projection.period_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Update if values changed
            if (projection.actual_revenue != actual_revenue or
                projection.actual_expenses != actual_expenses):
                projection.actual_revenue = actual_revenue
                projection.actual_expenses = actual_expenses
                projection.save()
                updated_count += 1

        return Response({
            'message': f'تم تحديث {updated_count} توقع للتدفق النقدي',
            'updated_count': updated_count
        })

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get cash flow summary statistics"""
        current_date = timezone.now().date()
        current_month_start = current_date.replace(day=1)
        current_year_start = current_date.replace(month=1, day=1)

        # Get all projections
        all_projections = self.get_queryset()

        # Current month projections
        current_month_projections = all_projections.filter(
            period_start__lte=current_date,
            period_end__gte=current_date
        )

        # Current year projections
        current_year_projections = all_projections.filter(
            period_start__year=current_date.year
        )

        # Calculate totals
        total_projected_revenue = all_projections.aggregate(
            total=Sum('projected_revenue')
        )['total'] or 0

        total_actual_revenue = all_projections.aggregate(
            total=Sum('actual_revenue')
        )['total'] or 0

        total_projected_expenses = all_projections.aggregate(
            total=Sum('projected_expenses')
        )['total'] or 0

        total_actual_expenses = all_projections.aggregate(
            total=Sum('actual_expenses')
        )['total'] or 0

        # Current month totals
        month_projected_revenue = current_month_projections.aggregate(
            total=Sum('projected_revenue')
        )['total'] or 0

        month_actual_revenue = current_month_projections.aggregate(
            total=Sum('actual_revenue')
        )['total'] or 0

        month_projected_expenses = current_month_projections.aggregate(
            total=Sum('projected_expenses')
        )['total'] or 0

        month_actual_expenses = current_month_projections.aggregate(
            total=Sum('actual_expenses')
        )['total'] or 0

        # Current year totals
        year_projected_revenue = current_year_projections.aggregate(
            total=Sum('projected_revenue')
        )['total'] or 0

        year_actual_revenue = current_year_projections.aggregate(
            total=Sum('actual_revenue')
        )['total'] or 0

        year_projected_expenses = current_year_projections.aggregate(
            total=Sum('projected_expenses')
        )['total'] or 0

        year_actual_expenses = current_year_projections.aggregate(
            total=Sum('actual_expenses')
        )['total'] or 0

        # Calculate variances
        revenue_variance = total_actual_revenue - total_projected_revenue
        expense_variance = total_actual_expenses - total_projected_expenses

        # Calculate accuracy percentages
        revenue_accuracy = (total_actual_revenue / total_projected_revenue * 100) if total_projected_revenue > 0 else 0
        expense_accuracy = (total_actual_expenses / total_projected_expenses * 100) if total_projected_expenses > 0 else 0

        summary_data = {
            'total_projections': all_projections.count(),
            'current_month_projections': current_month_projections.count(),
            'current_year_projections': current_year_projections.count(),
            'totals': {
                'projected_revenue': float(total_projected_revenue),
                'actual_revenue': float(total_actual_revenue),
                'projected_expenses': float(total_projected_expenses),
                'actual_expenses': float(total_actual_expenses),
                'projected_profit': float(total_projected_revenue - total_projected_expenses),
                'actual_profit': float(total_actual_revenue - total_actual_expenses)
            },
            'current_month': {
                'projected_revenue': float(month_projected_revenue),
                'actual_revenue': float(month_actual_revenue),
                'projected_expenses': float(month_projected_expenses),
                'actual_expenses': float(month_actual_expenses),
                'projected_profit': float(month_projected_revenue - month_projected_expenses),
                'actual_profit': float(month_actual_revenue - month_actual_expenses)
            },
            'current_year': {
                'projected_revenue': float(year_projected_revenue),
                'actual_revenue': float(year_actual_revenue),
                'projected_expenses': float(year_projected_expenses),
                'actual_expenses': float(year_actual_expenses),
                'projected_profit': float(year_projected_revenue - year_projected_expenses),
                'actual_profit': float(year_actual_revenue - year_actual_expenses)
            },
            'variances': {
                'revenue_variance': float(revenue_variance),
                'expense_variance': float(expense_variance),
                'revenue_accuracy': float(revenue_accuracy),
                'expense_accuracy': float(expense_accuracy)
            }
        }

        return Response(summary_data)


class BudgetViewSet(viewsets.ModelViewSet):
    """Budget management viewset"""
    queryset = Budget.objects.all().select_related('created_by').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['name', 'description']
    ordering_fields = ['total_budget', 'start_date', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return BudgetListSerializer
        return BudgetSerializer

    def perform_create(self, serializer):
        """Set created_by to current user"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a budget"""
        budget = self.get_object()
        
        if budget.status != 'draft':
            return Response(
                {'error': 'يمكن تفعيل المسودات فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        budget.status = 'active'
        budget.save()
        
        serializer = self.get_serializer(budget)
        return Response(serializer.data)


class FinancialKPIViewSet(viewsets.ModelViewSet):
    """Financial KPI management viewset"""
    queryset = FinancialKPI.objects.all().order_by('-period_start')
    serializer_class = FinancialKPISerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['type']
    ordering_fields = ['period_start', 'current_value', 'achievement_percentage']
    ordering = ['-period_start']

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get KPI dashboard data"""
        current_date = timezone.now().date()
        current_month_start = current_date.replace(day=1)

        # Get current month KPIs
        current_kpis = self.get_queryset().filter(
            period_start__lte=current_date,
            period_end__gte=current_date
        )

        kpi_data = []
        for kpi in current_kpis:
            kpi_data.append({
                'name': kpi.name,
                'type': kpi.type,
                'current_value': float(kpi.current_value),
                'target_value': float(kpi.target_value),
                'achievement_percentage': float(kpi.achievement_percentage),
                'status': kpi.status,
                'growth_rate': float(kpi.growth_rate)
            })

        return Response({
            'current_period': current_month_start.strftime('%Y-%m'),
            'kpis': kpi_data
        })

    @action(detail=False, methods=['post'])
    def auto_calculate(self, request):
        """Auto-calculate KPI values from financial data"""
        period_start = request.data.get('period_start')
        period_end = request.data.get('period_end')

        if not period_start or not period_end:
            return Response(
                {'error': 'يجب تحديد تاريخ البداية والنهاية'},
                status=status.HTTP_400_BAD_REQUEST
            )

        period_start = datetime.strptime(period_start, '%Y-%m-%d').date()
        period_end = datetime.strptime(period_end, '%Y-%m-%d').date()

        # Calculate revenue growth
        current_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=period_start,
            payment_date__lte=period_end
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate previous period revenue for growth rate
        period_length = (period_end - period_start).days
        prev_start = period_start - timedelta(days=period_length)
        prev_end = period_start - timedelta(days=1)

        prev_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=prev_start,
            payment_date__lte=prev_end
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate expenses
        current_expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=period_start,
            payment_date__lte=period_end
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate profit margin
        profit = current_revenue - current_expenses
        profit_margin = (profit / current_revenue * 100) if current_revenue > 0 else 0

        # Calculate revenue growth
        revenue_growth = ((current_revenue - prev_revenue) / prev_revenue * 100) if prev_revenue > 0 else 0

        calculated_kpis = {
            'revenue_growth': float(revenue_growth),
            'profit_margin': float(profit_margin),
            'total_revenue': float(current_revenue),
            'total_expenses': float(current_expenses),
            'net_profit': float(profit)
        }

        return Response(calculated_kpis)


class FinancialDashboardViewSet(viewsets.ViewSet):
    """Financial dashboard data viewset"""
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def overview(self, request):
        """Get financial overview for dashboard"""
        current_date = timezone.now().date()
        current_month_start = current_date.replace(day=1)
        current_year_start = current_date.replace(month=1, day=1)

        # Revenue metrics
        total_revenue = RevenueStream.objects.filter(
            status='received'
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        monthly_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=current_month_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        yearly_revenue = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=current_year_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Expense metrics
        total_expenses = Expense.objects.filter(
            status='paid'
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        monthly_expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=current_month_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        yearly_expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=current_year_start
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Pending items
        pending_invoices = RevenueStream.objects.filter(status='pending').count()
        overdue_invoices = RevenueStream.objects.filter(
            status='pending',
            due_date__lt=current_date
        ).count()
        pending_expenses = Expense.objects.filter(status='pending').count()

        # Calculate profit margins
        monthly_profit = monthly_revenue - monthly_expenses
        yearly_profit = yearly_revenue - yearly_expenses
        total_profit = total_revenue - total_expenses

        monthly_margin = (monthly_profit / monthly_revenue * 100) if monthly_revenue > 0 else 0
        yearly_margin = (yearly_profit / yearly_revenue * 100) if yearly_revenue > 0 else 0
        total_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

        overview_data = {
            'revenue': {
                'total': float(total_revenue),
                'monthly': float(monthly_revenue),
                'yearly': float(yearly_revenue)
            },
            'expenses': {
                'total': float(total_expenses),
                'monthly': float(monthly_expenses),
                'yearly': float(yearly_expenses)
            },
            'profit': {
                'total': float(total_profit),
                'monthly': float(monthly_profit),
                'yearly': float(yearly_profit)
            },
            'margins': {
                'total': float(total_margin),
                'monthly': float(monthly_margin),
                'yearly': float(yearly_margin)
            },
            'pending': {
                'invoices': pending_invoices,
                'overdue_invoices': overdue_invoices,
                'expenses': pending_expenses
            }
        }

        return Response(overview_data)

    @action(detail=False, methods=['get'])
    def department_analysis(self, request):
        """Get financial analysis by department"""
        departments = ['sales', 'development', 'design', 'media_buying']
        department_data = []

        for dept in departments:
            # Revenue from projects handled by this department
            dept_revenue = RevenueStream.objects.filter(
                status='received',
                project__team_members__department=dept
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Expenses for this department (team salaries + project costs)
            dept_expenses = Expense.objects.filter(
                status='paid',
                team_member__department=dept
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Project count
            from projects.models import Project
            project_count = Project.objects.filter(
                team_members__department=dept
            ).distinct().count()

            # Average project value
            avg_project_value = dept_revenue / project_count if project_count > 0 else 0

            # Profit margin
            dept_profit = dept_revenue - dept_expenses
            profit_margin = (dept_profit / dept_revenue * 100) if dept_revenue > 0 else 0

            department_data.append({
                'department': dept,
                'total_revenue': float(dept_revenue),
                'total_expenses': float(dept_expenses),
                'profit_margin': float(profit_margin),
                'team_cost': float(dept_expenses),
                'project_count': project_count,
                'avg_project_value': float(avg_project_value)
            })

        return Response(department_data)

    @action(detail=False, methods=['get'])
    def trends(self, request):
        """Get financial trends data"""
        months = int(request.query_params.get('months', 12))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30 * months)

        trends_data = []

        for i in range(months):
            month_start = start_date + timedelta(days=30 * i)
            month_end = month_start + timedelta(days=30)

            month_revenue = RevenueStream.objects.filter(
                status='received',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            month_expenses = Expense.objects.filter(
                status='paid',
                payment_date__gte=month_start,
                payment_date__lt=month_end
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            month_profit = month_revenue - month_expenses

            trends_data.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': float(month_revenue),
                'expenses': float(month_expenses),
                'profit': float(month_profit)
            })

        return Response(trends_data)


class FinancialReportsViewSet(viewsets.ModelViewSet):
    """Financial reports management viewset"""
    queryset = FinancialReport.objects.all().select_related('generated_by').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status', 'period']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return FinancialReportListSerializer
        return FinancialReportSerializer

    def perform_create(self, serializer):
        """Set the generated_by field to current user"""
        serializer.save(generated_by=self.request.user)

    @action(detail=False, methods=['post'])
    def generate(self, request):
        """Generate a new financial report"""
        report_type = request.data.get('type')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        title = request.data.get('title', f'تقرير {report_type}')

        if not all([report_type, start_date, end_date]):
            return Response(
                {'error': 'يجب تحديد نوع التقرير وتاريخ البداية والنهاية'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create report instance
        report = FinancialReport.objects.create(
            title=title,
            type=report_type,
            start_date=start_date,
            end_date=end_date,
            status='generating',
            period='custom',
            generated_by=request.user
        )

        # Generate report data based on type
        try:
            report_data = self._generate_report_data(report_type, start_date, end_date)
            report.report_data = report_data
            report.status = 'completed'
            report.save()

            serializer = self.get_serializer(report)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            report.status = 'failed'
            report.save()
            return Response(
                {'error': f'فشل في إنشاء التقرير: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download report as file"""
        report = self.get_object()

        if report.status != 'completed':
            return Response(
                {'error': 'التقرير غير مكتمل'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # For now, return the JSON data
        # In production, you might want to generate PDF/Excel files
        from django.http import JsonResponse
        response = JsonResponse(report.report_data, json_dumps_params={'ensure_ascii': False})
        response['Content-Disposition'] = f'attachment; filename="{report.title}.json"'
        return response

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get reports summary statistics"""
        queryset = self.get_queryset()

        total_reports = queryset.count()
        completed_reports = queryset.filter(status='completed').count()
        failed_reports = queryset.filter(status='failed').count()
        generating_reports = queryset.filter(status='generating').count()

        # Reports by type
        reports_by_type = queryset.values('type').annotate(
            count=Count('id')
        ).order_by('-count')

        summary = {
            'total_reports': total_reports,
            'completed_reports': completed_reports,
            'failed_reports': failed_reports,
            'generating_reports': generating_reports,
            'reports_by_type': list(reports_by_type)
        }

        return Response(summary)

    def _generate_report_data(self, report_type, start_date, end_date):
        """Generate report data based on type"""
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        if report_type == 'profit_loss':
            return self._generate_profit_loss_report(start_date, end_date)
        elif report_type == 'cash_flow':
            return self._generate_cash_flow_report(start_date, end_date)
        elif report_type == 'revenue_analysis':
            return self._generate_revenue_analysis_report(start_date, end_date)
        elif report_type == 'expense_analysis':
            return self._generate_expense_analysis_report(start_date, end_date)
        elif report_type == 'department_performance':
            return self._generate_department_performance_report(start_date, end_date)
        else:
            return {'message': 'نوع التقرير غير مدعوم حالياً'}

    def _generate_profit_loss_report(self, start_date, end_date):
        """Generate profit and loss report"""
        # Revenue data
        revenues = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=start_date,
            payment_date__lte=end_date
        )
        total_revenue = revenues.aggregate(total=Sum('net_amount'))['total'] or 0

        # Expense data
        expenses = Expense.objects.filter(
            status='paid',
            payment_date__gte=start_date,
            payment_date__lte=end_date
        )
        total_expenses = expenses.aggregate(total=Sum('net_amount'))['total'] or 0

        # Calculate profit
        net_profit = total_revenue - total_expenses
        profit_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0

        return {
            'period': f'{start_date} إلى {end_date}',
            'total_revenue': float(total_revenue),
            'total_expenses': float(total_expenses),
            'net_profit': float(net_profit),
            'profit_margin': float(profit_margin),
            'revenue_breakdown': list(revenues.values('type').annotate(
                total=Sum('net_amount')
            )),
            'expense_breakdown': list(expenses.values('type').annotate(
                total=Sum('net_amount')
            ))
        }

    def _generate_cash_flow_report(self, start_date, end_date):
        """Generate cash flow report"""
        # Cash inflows (received revenues)
        inflows = RevenueStream.objects.filter(
            status='received',
            payment_date__gte=start_date,
            payment_date__lte=end_date
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        # Cash outflows (paid expenses)
        outflows = Expense.objects.filter(
            status='paid',
            payment_date__gte=start_date,
            payment_date__lte=end_date
        ).aggregate(total=Sum('net_amount'))['total'] or 0

        net_cash_flow = inflows - outflows

        return {
            'period': f'{start_date} إلى {end_date}',
            'cash_inflows': float(inflows),
            'cash_outflows': float(outflows),
            'net_cash_flow': float(net_cash_flow)
        }

    def _generate_revenue_analysis_report(self, start_date, end_date):
        """Generate revenue analysis report"""
        revenues = RevenueStream.objects.filter(
            payment_date__gte=start_date,
            payment_date__lte=end_date
        )

        total_revenue = revenues.aggregate(total=Sum('net_amount'))['total'] or 0
        revenue_by_type = revenues.values('type').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')

        return {
            'period': f'{start_date} إلى {end_date}',
            'total_revenue': float(total_revenue),
            'revenue_by_type': list(revenue_by_type),
            'average_revenue_per_stream': float(total_revenue / revenues.count()) if revenues.count() > 0 else 0
        }

    def _generate_expense_analysis_report(self, start_date, end_date):
        """Generate expense analysis report"""
        expenses = Expense.objects.filter(
            expense_date__gte=start_date,
            expense_date__lte=end_date
        )

        total_expenses = expenses.aggregate(total=Sum('net_amount'))['total'] or 0
        expenses_by_type = expenses.values('type').annotate(
            total=Sum('net_amount'),
            count=Count('id')
        ).order_by('-total')

        return {
            'period': f'{start_date} إلى {end_date}',
            'total_expenses': float(total_expenses),
            'expenses_by_type': list(expenses_by_type),
            'average_expense': float(total_expenses / expenses.count()) if expenses.count() > 0 else 0
        }

    def _generate_department_performance_report(self, start_date, end_date):
        """Generate department performance report"""
        departments = ['sales', 'development', 'design', 'media_buying']
        department_data = []

        for dept in departments:
            # Revenue from projects handled by this department
            dept_revenue = RevenueStream.objects.filter(
                status='received',
                payment_date__gte=start_date,
                payment_date__lte=end_date,
                project__team_members__department=dept
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            # Expenses for this department
            dept_expenses = Expense.objects.filter(
                status='paid',
                payment_date__gte=start_date,
                payment_date__lte=end_date,
                team_member__department=dept
            ).aggregate(total=Sum('net_amount'))['total'] or 0

            dept_profit = dept_revenue - dept_expenses
            profit_margin = (dept_profit / dept_revenue * 100) if dept_revenue > 0 else 0

            department_data.append({
                'department': dept,
                'revenue': float(dept_revenue),
                'expenses': float(dept_expenses),
                'profit': float(dept_profit),
                'profit_margin': float(profit_margin)
            })

        return {
            'period': f'{start_date} إلى {end_date}',
            'departments': department_data
        }
